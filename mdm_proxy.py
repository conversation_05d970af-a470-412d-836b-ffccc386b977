import requests
import custom_logger
import common

class MDMProxy:
    def __init__(self, config):
        self.config = config

    def get_suppliers(self, params):
        response = requests.get(
            url=self.config['url'] + f"/mdm/supplier/v1.0.1/{self.config['bu']}/APP/suppliers",
            params=params
        )

        if common.is_successful_http_status(response.status_code):
            custom_logger.logger.info(
                f"Call GetSupplier successfully [bu = {self.config['bu']}]")
            return response.json()
        else:
            custom_logger.logger.error(
                f"Failed to call GetSupplier [bu = {self.config['bu']}]")
            raise Exception(
                f"Failed to call GetSupplier [statusCode = {response.status_code}, detail = {response.text}]")
