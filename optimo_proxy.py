import requests
import custom_logger
import time
import hashlib
import common

class OptimOProxy:
    def __init__(self, config):
        self.token = None
        self.config = config
        self.refresh_token()

    def refresh_token(self):
        authorization_data = {
            'grant_type': 'client_credentials',
            'provision_key': 'techlab_mp',
            'client_id': self.config['client_id'],
            'client_secret': self.config['client_secret']
        }

        response = requests.post(
            self.config['url'] + '/mp/oauth2/token',
            data = authorization_data,
            headers={
                'Accept': 'application/json'
            }
        )

        if common.is_successful_http_status(response.status_code):
            self.token = response.json()['access_token']
            custom_logger.logger.info(
                f"Successfully obtained token [url = {self.config['url']}, bu = {self.config['bu']}]")
        else:
            custom_logger.logger.error(f"Failed to obtain token [url = {self.config['url']}, bu = {self.config['bu']}]")
            raise Exception(f"Failed to obtain token [statusCode = {response.status_code}, detail = {response.text}]")

    def get_headers(self):
        timestamp = time.time()
        sec_timestamp = int(timestamp)

        signature = f"{self.config['appid']}{self.config['sign_token']}{sec_timestamp}"
        return {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.token}',
            'Accept': 'application/json',
            'appid': f"{self.config['appid']}",
            'timestamp': f"{sec_timestamp}",
            'sign': f"{hashlib.md5(signature.encode()).hexdigest()}",
        }

    def ad_material(self, data):
        response = requests.post(
            self.config['url'] + f"/mp/v1.0.0/{self.config['bu']}/ad/AdMaterial",
            json=data,
            headers=self.get_headers()
        )

        if common.is_successful_http_status(response.status_code):
            custom_logger.logger.info(
                f"Call AdMaterial successfully [bu = {self.config['bu']}, appid = {self.config['appid']}]")
            return response.json()
        else:
            custom_logger.logger.error(f"Failed to call AdMaterial [bu = {self.config['bu']}, appid = {self.config['appid']}]")
            raise Exception(f"Failed to call AdMaterial [statusCode = {response.status_code}, detail = {response.text}]")

    def click_event(self, params):
        response = requests.get(
            url=self.config['url'] + f"/mp/v1.0.0/{self.config['bu']}/ad/ClickEvent",
            headers=self.get_headers(),
            params=params
        )

        if common.is_successful_http_status(response.status_code):
            custom_logger.logger.info(
                f"Call ClickEvent successfully [bu = {self.config['bu']}, appid = {self.config['appid']}]")
            return response.json()
        else:
            custom_logger.logger.error(
                f"Failed to call ClickEvent [bu = {self.config['bu']}, appid = {self.config['appid']}]")
            raise Exception(
                f"Failed to call ClickEvent [statusCode = {response.status_code}, detail = {response.text}]")

    def exposure_event(self, params):
        response = requests.get(
            url=self.config['url'] + f"/mp/v1.0.0/{self.config['bu']}/ad/ExposureEvent",
            headers=self.get_headers(),
            params=params
        )

        if common.is_successful_http_status(response.status_code):
            custom_logger.logger.info(
                f"Call ExposureEvent successfully [bu = {self.config['bu']}, appid = {self.config['appid']}]")
            return response.json()
        else:
            custom_logger.logger.error(
                f"Failed to call ExposureEvent [bu = {self.config['bu']}, appid = {self.config['appid']}]")
            raise Exception(
                f"Failed to call ExposureEvent [statusCode = {response.status_code}, detail = {response.text}]")

    def sync_tab(self, data):
        timestamp = time.time() * 1000
        signature = f"{self.config['appid']}{self.config['sign_token']}{timestamp}carouselStatus={data['carouselStatus']}&heroBannerStatus={data['heroBannerStatus']}&homePageBannerStatus={data['homePageBannerStatus']}&id={data['id']}&offlineDate={data['offlineDate']}&onlineDate={data['onlineDate']}&sliderBannerStatus={data['sliderBannerStatus']}&tabID={data['tabID']}"

        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.token}',
            'Accept': 'application/json',
            'appid': f"{self.config['appid']}",
            'timestamp': f"{timestamp}",
            'sign': f"{hashlib.md5(signature.encode()).hexdigest()}",
        }

        response = requests.post(
            self.config['url'] + f"/mp/v1.0.0/{self.config['bu']}/admin/tab",
            json=data,
            headers=headers
        )

        if common.is_successful_http_status(response.status_code):
            custom_logger.logger.info(
                f"Call SyncTab successfully [bu = {self.config['bu']}, appid = {self.config['appid']}]")
            return response.json()
        else:
            custom_logger.logger.error(f"Failed to call SyncTab [bu = {self.config['bu']}, appid = {self.config['appid']}]")
            raise Exception(f"Failed to call SyncTab [statusCode = {response.status_code}, detail = {response.text}]")