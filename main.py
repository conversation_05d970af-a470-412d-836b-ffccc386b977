import optimo_proxy
import custom_logger
import mdm_proxy

ad_configs = [
    {
        'bu': 'PNSHK',
        'appid': 1,
        'sign_token': 'RT@D4*p*A218^OFdDfj$',
        'client_id': 'PNSHK',
        'client_secret': 'PNSHK-!QAZ2wsx',
    },
    {
        'bu': 'FTRHK',
        'appid': 1,
        'sign_token': 'RT@D4*p*A218^OFdDfj$',
        'client_id': 'mp-ftrhk-prod',
        'client_secret': 'bBBWssZJuCxSXWkSIPUPgCPWWSWE1c2z',
    },
    {
        'bu': 'WTCHK',
        'appid': 1,
        'sign_token': 'RT@D4*p*A218^OFdDfj$',
        'client_id': 'mp-wtchk-prod',
        'client_secret': '13E940TeV3OyMHnO8fXajCIhAH8BXZH5',
    },
    {
        'bu': 'WTCSG',
        'appid': 1,
        'sign_token': 'RT@D4*p*A218^OFdDfj$',
        'client_id': 'mp-wtcsg-prod',
        'client_secret': '2GZr83iNEMI5PwlwEC3d2QXvzd87wiPq',
    },
    {
        'bu': 'WTCID',
        'appid': 1,
        'sign_token': 'RT@D4*p*A218^OFdDfj$',
        'client_id': 'mp-wtcid-prod',
        'client_secret': 'FrBLH7jYjxeFbpj3JkZZhldvpmJkbXjF',
    },
    {
        'bu': 'WTCTH',
        'appid': 1,
        'sign_token': 'RT@D4*p*A218^OFdDfj$',
        'client_id': 'mp-wtcth-prod',
        'client_secret': 'JzsRhsNvHPSkvbYbLGeqiE6e3ee6en6N',
    },
    {
        'bu': 'WTCPH',
        'appid': 1,
        'sign_token': 'RT@D4*p*A218^OFdDfj$',
        'client_id': 'mp-wtcph-prod',
        'client_secret': 'Bc8YEb4YVUNt338SJ35r4mjmMMjkj147',
    },
    {
        'bu': 'WTCMY',
        'appid': 1,
        'sign_token': 'RT@D4*p*A218^OFdDfj$',
        'client_id': 'mp-wtcmy-prod',
        'client_secret': 'oLHC8x5I7AQLRTQqEKWzsUKF1e7aX6Y2',
    },
    {
        'bu': 'WTCTW',
        'appid': 1,
        'sign_token': 'RT@D4*p*A218^OFdDfj$',
        'client_id': 'mp-wtctw-prod',
        'client_secret': 'MMM15E1KX8Eub7b2bE0qJnmciSPZk8XE',
    }
]

def test_ad_material(proxy):
    try:
        rsp = proxy.ad_material(
            {
                "id": 1733301496382,
                "user": {
                    "visible_card_num": "2599014694605"
                },
                "device": {
                    "language": "en"
                },
                "imp": [
                    {
                        "id": 1733301496382,
                        "page_category": "SubCategoryBanner",
                        "page_type_code":"10010204"
                    }
                ]
            }
        )

        custom_logger.logger.info(f"Succeed to call AdMaterial [{rsp}]")
    except Exception as e:
        custom_logger.logger.error(f"Failed to call AdMaterial [{e}]")

def test_click_event(proxy):
    try:
        rsp = proxy.click_event({
            'l': 0
        })

        custom_logger.logger.info(f"Succeed to call ExposureEvent [{rsp}]")
    except Exception as e:
        custom_logger.logger.error(f"Failed to call ClickEvent [{e}]")

def test_exposure_event(proxy):
    try:
        rsp = proxy.exposure_event({
            'l': 0
        })

        custom_logger.logger.info(f"Succeed to call ExposureEvent [{rsp}]")
    except Exception as e:
        custom_logger.logger.error(f"Failed to call ExposureEvent [{e}]")

def run_ad_tests(config):
    proxy = optimo_proxy.OptimOProxy(config)
    custom_logger.logger.info(config)

    test_ad_material(proxy)
    test_click_event(proxy)
    test_exposure_event(proxy)
    run_tab_test(config)

def run_tab_test(config):
    proxy = optimo_proxy.OptimOProxy(config)
    try:
        rsp = proxy.sync_tab({
            "id": "",
            "tabID": "",
            "onlineDate": 0,
            "offlineDate": 0,
            "homePageBannerStatus": "off",
            "heroBannerStatus": "off",
            "sliderBannerStatus": "off",
            "carouselStatus": "off",
            "tabNames": ""
        })

        custom_logger.logger.info(config)
        custom_logger.logger.info(f"Succeed to call SyncTab [{rsp}]")
    except Exception as e:
        custom_logger.logger.error(f"Failed to call SyncTab [{e}]")

def find_not_allocated_suppliers(config):
    proxy = mdm_proxy.MDMProxy(config)
    supplier_ids = ["1061375014",
    "1097964510",
    "1030585051",
    "1001739071",
    "1005355011",
    "1022610119",
    "1000499010",
    "1022488051",
    "6262",
    "1085635010",
    "1066748040",
    "1080886010",
    "1049538010",
    "1000496800",
    "1070583010",
    "1034212104",
    "1071057010",
    "1054531051",
    "1006476061",
    "1005355103",
    "1000496060",
    "1005355011",
    "1008507101",
    "1002120069",
    "1003806014",
    "1005544091",
    "1040328010",
    "1054105040",
    "1053347040",
    "1062734040",
    "1094503010",
    "1003805052",
    "1004560012",
    "1096414010",
    "1002119101",
    "1006648021",
    "1000499103",
    "1022610135",
    "1003431010",
    "1052381500",
    "1028293010",
    "1114587501",
    "1071590501",
    "1003458050",
    "1000301010",
    "1004599010",
    "1000084043",
    "1004395010",
    "1109109510",
    "1051586010",
    "1006607052",
    "1042865040",
    "1098003500",
    "1066820500",
    "1002331010",
    "1003771060",
    "1004366042",
    "1006125050",
    "1002489011",
    "1089576016",
    "1004560012",
    "1006118050",
    "1034190517",
    "1006607050",
    "1003402052",
    "1049431510",
    "1002489010",
    "1034190058",
    "1005713041",
    "1001835010",
    "1046729500",
    "1030502012",
    "1039176040",
    "1096302510",
    "1000725050",
    "1005643050",
    "1034190518",
    "1043321060",
    "1002078061",
    "1114664510",
    "1000622010",
    "1034190516",
    "1002744050",
    "1069463021",
    "1089576014",
    "1005661020",
    "1001138010",
    "1095298024",
    "1001205010",
    "1005158011",
    "1002079060",
    "1001217050",
    "1031430060",
    "1115278010",
    "1001534010",
    "1003771061",
    "1077783010",
    "1096187024",
    "1002489060",
    "1005454015",
    "1103625510",
    "1002007022",
    "1073413050",
    "1000930012",
    "1000084012",
    "1001231010",
    "1074395061",
    "1047816010",
    "1039297020",
    "1004560013",
    "1006549060",
    "1001500510",
    "1053756060",
    "1103906500",
    "1110907060",
    "1002380060",
    "1003402051",
    "1089576040",
    "1062515510",
    "1079305500",
    "1056164021",
    "1097565010",
    "1049538010",
    "1040359020",
    "1112387060",
    "1061375011",
    "1024004060",
    "1001031010",
    "1032523014",
    "1000365010",
    "1034190501",
    "1000080020",
    "1001689050",
    "1004395041",
    "1109856510",
    "1001689510",
    "1062238045",
    "1001534012",
    "1003806010",
    "1091852010",
    "1002650043",
    "1063281040",
    "1004823040",
    "1006476010",
    "1095836041",
    "1003546513",
    "1003827040",
    "1087451030",
    "1089576502",
    "1002005010",
    "1034190014",
    "1001689051",
    "1083187510",
    "1003546514",
    "1001663041",
    "1038899011",
    "1003339010",
    "1063605010",
    "1000776010",
    "1103446023",
    "1004560018",
    "1000365012",
    "1068756020",
    "1005371012",
    "1004726010",
    "1002639050",
    "1034190015",
    "1004511511",
    "1000843063",
    "1110651040",
    "1040628010",
    "1049790060",
    "1097650060",
    "1005703011",
    "1040327041",
    "1000533010",
    "1066955011",
    "1001348040",
    "1002489051",
    "1002242010",
    "1004372010",
    "1005958062",
    "1047676010",
    "1006662032",
    "1100148030",
    "1074395062",
    "1002489066",
    "1076305041",
    "1081675011",
    "1001534062",
    "1002101011",
    "1030563011",
    "1039655040",
    "1007253040",
    "1004366040",
    "1074429011",
    "1004874010",
    "1001174011",
    "1090748010",
    "1000028052",
    "1019007010",
    "1006176013",
    "1004344010",
    "1001444011",
    "1005158013",
    "1043910011",
    "1004736011",
    "1000406061",
    "1095836042",
    "1116400800",
    "1010786800",
    "1001854050",
    "1005871040",
    "1006965500",
    "1089576013",
    "1096434040",
    "1000209050",
    "1034699010",
    "1004833032",
    "1001854500",
    "1000084010",
    "1080828500",
    "1032449022",
    "1089576018",
    "1004599060",
    "1034190523",
    "1040313040",
    "1050501060",
    "1005658010",
    "1000365510",
    "1004599062",
    "1092342062",
    "1001040501",
    "1062238040",
    "1005272051",
    "1004395052",
    "1083579010",
    "1047923500",
    "1000351501",
    "1002547011",
    "1001788041",
    "1004469061",
    "1073582500",
    "1059988010",
    "1027527010",
    "1006325010",
    "1101379010",
    "1000622062",
    "1094446040",
    "1032370013",
    "1098973010",
    "1034190519",
    "1073458040",
    "1090281040",
    "1000725010",
    "1008486060",
    "1081221030",
    "1010786050",
    "1052301010",
    "1003056010",
    "1006722010",
    "1006476061",
    "1003246010",
    "1006380010",
    "1005982050",
    "1062238044",
    "1032523062",
    "1010579042",
    "1110907040",
    "1103504500",
    "1080296500",
    "1087650510",
    "1033992011",
    "1031283063",
    "1028897060",
    "1025888011",
    "1032271060",
    "1001534041",
    "1003806011",
    "1089576017",
    "1097677060",
    "1067137060",
    "1005708041",
    "1005708040",
    "1024832010",
    "1068554010",
    "1006176022",
    "1000486064",
    "1005819040",
    "1061628040",
    "1005130013",
    "1003570010",
    "1004395054",
    "1036353010",
    "1007591020",
    "1038899010",
    "1004855065",
    "1000301012",
    "1005854024",
    "1005130016",
    "1109539010",
    "1000674050",
    "1000496800",
    "1010796010",
    "1114759010",
    "1094134060",
    "1002516061",
    "1034190521",
    "1064132500",
    "1079303051",
    "1034190042",
    "1116859010",
    "1000499010",
    "1000301011",
    "1002481040",
    "1111646010",
    "1000209051",
    "1002489064",
    "1001231013",
    "1114587502",
    "1003339011",
    "1006301051",
    "1031331012",
    "1061375013",
    "1004348011",
    "1035069010",
    "1073276501",
    "1001835060",
    "1049431511",
    "1033462040",
    "1034190010",
    "1060329010",
    "1002908010",
    "1115754060",
    "1062995040",
    "1034190056",
    "1035722040",
    "1008487010",
    "1004348010",
    "1043150050",
    "1081022010",
    "1000552010",
    "1001534013",
    "1003032050",
    "1036310061",
    "1006543050",
    "1089576010",
    "1006245012",
    "1074395066",
    "1035021010",
    "1005272050",
    "1048306040",
    "1063310020",
    "1004381040",
    "1055764060",
    "1082442060",
    "1062238011",
    "1000677010",
    "1005971051",
    "1001763041",
    "1005661011",
    "1005745011",
    "1029226510",
    "1000406010",
    "1001471011",
    "1019102061",
    "1006245061",
    "1030563010",
    "1034670010",
    "1090761101",
    "1004395012",
    "1114305500",
    "1089576042",
    "1000198511",
    "1104171024",
    "1032523020",
    "1083568510",
    "1002079064",
    "1099478040",
    "1082974061",
    "1098075010",
    "1004599041",
    "1000028053",
    "1049227020",
    "1004469010",
    "1004855014",
    "1055761040",
    "1089576501",
    "1005729010",
    "1057431060",
    "1006122010",
    "1001763040",
    "1061375010",
    "1089576500",
    "1075806510",
    "1073948020",
    "1010564040",
    "1060697060",
    "1004833020",
    "1002489065",
    "1098973060",
    "1055053061",
    "1050931010",
    "1091089040",
    "1087846500",
    "1003634010",
    "1079124510",
    "1005110111",
    "1003864040",
    "1062288500",
    "1109948060",
    "1034190510",
    "1099158040",
    "1000382016",
    "1019102020",
    "1088324040",
    "1057262011",
    "1089776020",
    "1000617041",
    "1088192012",
    "1028256020",
    "1001177060",
    "1081158010",
    "1113670010",
    "1083110510",
    "1003261060",
    "1000930036",
    "1075425031",
    "1000503010",
    "1002431030",
    "1006151070",
    "1002744500",
    "1048329030",
    "1067720040",
    "1093713020",
    "1095836040",
    "1000674052",
    "1062238041",
    "1000301013",
    "1000843060",
    "1004833061",
    "1047719500",
    "1006275070",
    "1005130040",
    "1005528012",
    "1004736010",
    "1061375014",
    "1000781010",
    "1114305501",
    "1005879011",
    "1058314010",
    "1077643061",
    "1001221010",
    "1006301050",
    "1096711070",
    "1078157500",
    "1000205040",
    "1106870040",
    "1001534042",
    "1083368010",
    "1110630010",
    "1090761500",
    "1093854500",
    "1111079040",
    "1028587090",
    "1001261011",
    "1086579040",
    "1005745012",
    "1036513020",
    "1004515055",
    "1000930030",
    "1004284050",
    "1116706020",
    "1035967040",
    "1004511050",
    "1060735510",
    "1001739071",
    "1067481040",
    "1003133051",
    "1043729010",
    "1003947010",
    "1005110011",
    "1096713020",
    "1096383021",
    "1028222031",
    "1101333040",
    "1101410010",
    "1001835011",
    "1002029010",
    "1081325010",
    "1078850040",
    "1000084044",
    "1030585051",
    "1093033010",
    "1069026030",
    "1087847010",
    "1002079068",
    "1083721510",
    "1091523510",
    "1090897010",
    "1005371061",
    "1100941041",
    "1055845500",
    "1089576011",
    "1005130011",
    "1006624010",
    "1103909060",
    "1004560060",
    "1004037040",
    "1073276500",
    "1040037500",
    "1065187510",
    "1000143011",
    "1061578060",
    "1097924024",
    "1000198121",
    "1003275010",
    "1004951011",
    "1004800052",
    "1039957010",
    "1081400500",
    "1002702014",
    "1081496022",
    "1081801500",
    "1006132500",
    "1081410500",
    "1091209021",
    "1078750040",
    "1030502070",
    "1002541010",
    "1003546511",
    "1070886040",
    "1069516040",
    "1065066010",
    "1001788040",
    "1027527011",
    "1004440050",
    "1002965050",
    "1090600020",
    "1116596500",
    "1080613021",
    "1099996021",
    "1699",
    "1071057011",
    "1076989030",
    "1024672030",
    "1111631010",
    "1000301014",
    "1096980050",
    "1039047011",
    "1000282012",
    "1030502010",
    "1039149010",
    "1041074021",
    "1089576015",
    "1003339015",
    "1042658010",
    "1063683501",
    "1005676010",
    "1111861010",
    "1071191502",
    "1004844040",
    "1099478062",
    "1066356060",
    "1003366510",
    "1001842010",
    "1003771040",
    "1005228010",
    "1096382510",
    "1062140060",
    "1001031014",
    "1003261063",
    "1116640060",
    "1000108011",
    "1061375012",
    "1061628041",
    "1000113010",
    "1049880010",
    "1043910020",
    "1115551510",
    "1093342500",
    "1060467500",
    "1040347010",
    "1055643010",
    "1034190057",
    "1028485050",
    "1000725052",
    "1042209060",
    "1046733060",
    "1008290050",
    "1089576060",
    "1040650010",
    "1100852010",
    "1057431010",
    "1039403510",
    "1004736013",
    "1111672510",
    "1000125040",
    "1000577500",
    "1110621510",
    "1080886010",
    "1043789040",
    "1006665060",
    "1002362042",
    "1078768010",
    "1002800016",
    "1000714062",
    "1003546512",
    "1001534061",
    "1001587031",
    "1004395510",
    "1088334020",
    "1042473010",
    "1004050030",
    "1048584050",
    "1034190154",
    "1088093020",
    "1066150510",
    "1115591060",
    "1095069060",
    "1104023040",
    "1030559010",
    "1091721500",
    "1003864050",
    "1002431011",
    "1112280030",
    "1039428020",
    "1102871070",
    "1032523013",
    "1106870080",
    "1028256010",
    "1053946040",
    "1064648040",
    "1060329011",
    "1000121010",
    "1001739060",
    "1030153061",
    "1084529011",
    "1002702060",
    "1050718010",
    "1024849501",
    "1002016010",
    "1002493010",
    "1079528020",
    "1006507020",
    "1000301043",
    "1095311050",
    "1005735010",
    "1005136053",
    "1100850500",
    "1101405010",
    "1073564021",
    "1002406010",
    "1047740040",
    "1004739500",
    "1094503010",
    "1082974040",
    "1043363040",
    "1101332510",
    "1028143010",
    "1033391060",
    "1004584800",
    "1111497510",
    "1003166061",
    "1003546139",
    "1082606060",
    "1083265060",
    "1083576060",
    "1078937010",
    "1052622020",
    "1064878030",
    "1084529010",
    "1004348012",
    "1071820020",
    "1088653010",
    "1054322040",
    "1090081010",
    "1002344050",
    "1004833062",
    "1100402500",
    "1004708033",
    "1088161060",
    "1093631101",
    "1095955020",
    "1003546507",
    "1043321012",
    "1002554052",
    "1032310011",
    "1004474026",
    "1041015020",
    "1080948020",
    "1095868040",
    "1099478060",
    "1004620010",
    "1086963020",
    "1100023011",
    "1095482500",
    "1002380102",
    "1091500500",
    "1074395064",
    "1021496040",
    "1002079010",
    "1081675060",
    "1032868041",
    "1039574040",
    "1097028040",
    "1071706040",
    "1006648091",
    "1096261060",
    "1000394060",
    "1001534063",
    "1002331021",
    "1004874012",
    "1101089510",
    "1075692510",
    "1100887040",
    "1006356040",
    "1007607010",
    "1083709040",
    "1036310022",
    "1000608040",
    "1006213010",
    "1034190140",
    "1039520064",
    "1040984040",
    "1071705020",
    "1002268041",
    "1112516081",
    "1007306010",
    "1002007061",
    "1001689053",
    "1000301015",
    "1001157011",
    "1083998010",
    "1001568064",
    "1005958013",
    "1000084046",
    "1005298011",
    "1061096021",
    "1071191501",
    "1071116010",
    "1095523020",
    "1003339062",
    "1034190012",
    "1005937040",
    "1089576504",
    "1089039020",
    "1019102024",
    "1006965011",
    "1082989010",
    "1004599026",
    "1033426010",
    "1000930027",
    "1001157010",
    "1064128010",
    "1051572510",
    "1002501020",
    "1099478061",
    "1100852011",
    "1092342063",
    "1001861050",
    "1033497041",
    "1057432020",
    "1048471020",
    "1005653501",
    "1002982510",
    "1007171101",
    "1001031060",
    "1096169512",
    "1005110010",
    "1053212011",
    "1088102010",
    "1060545010",
    "1098202040",
    "1001309032",
    "1040313060",
    "1088706500",
    "1000500040",
    "1003913011",
    "1038975010",
    "1040938010",
    "1003203040",
    "1099467010",
    "1089911060",
    "1089576041",
    "1000499050",
    "1067852040",
    "1034190524",
    "1095836043",
    "1008129040",
    "1005745061",
    "1008521071",
    "1005528011",
    "1018897011",
    "1035124011",
    "1042508061",
    "1086963061",
    "1042657010",
    "1030502040",
    "1043288040",
    "1111617500",
    "1028293010",
    "1097964510",
    "1004855030",
    "1004123010",
    "1057659010",
    "1088786010",
    "1068825010",
    "1101208020",
    "1090897500",
    "1078292010",
    "1067920510",
    "1000725051",
    "1033424010",
    "1092342020",
    "1000121021",
    "1004560061",
    "1094644060",
    "1078784010",
    "1029732041",
    "1099421040",
    "1003339016",
    "1000394022",
    "1024002070",
    "1066955012",
    "1004620020",
    "1092342060",
    "1018645040",
    "1022666010",
    "1094913040",
    "1060467010",
    "1034183031",
    "1002362012",
    "1068554011",
    "1002701015",
    "1074395040",
    "1110421040",
    "1092342061",
    "1067481020",
    "1004858050",
    "1099862010",
    "1003193013",
    "1034971040",
    "1003570040",
    "1094503060",
    "1005770031",
    "1043184060",
    "1000184050",
    "1000873500",
    "1102871071",
    "1053756020",
    "1057172040",
    "1102987040",
    "1086248010",
    "1030408020",
    "1018898020",
    "1088705050",
    "1083245040",
    "1036004020",
    "1097964010",
    "1031430012",
    "1079793040",
    "1083721010",
    "1007306060",
    "1039200510",
    "1000714020",
    "1000282013",
    "1000930031",
    "1022665501",
    "1022584023",
    "1007607013",
    "1048611010",
    "1006459042",
    "1005471040",
    "1000486012",
    "1051215070",
    "1000108080",
    "1089576503",
    "1096884040",
    "1006549023",
    "1102725050",
    "1003854023",
    "1059492010",
    "1000282010",
    "1005429020",
    "1060328010",
    "1092192500",
    "1052793050",
    "1001744051",
    "1035406510",
    "1043791040",
    "1109162510",
    "1083705010",
    "1082945021",
    "1018864060",
    "1102091020",
    "1075964021",
    "1082353030",
    "1079935510",
    "1000080021",
    "1004800102",
    "1075807040",
    "1032523031",
    "1096189500",
    "1004599012",
    "1001534500",
    "1000198041",
    "1101380060",
    "1001995050",
    "1091528030",
    "1087982030",
    "1000301510",
    "1003792040",
    "1066748040",
    "1004594010",
    "1004560020",
    "1005158010",
    "1111618040",
    "1000714021",
    "1028256070",
    "1090602070",
    "1114954500",
    "1004211514",
    "1052540030",
    "1000365013",
    "1001534022",
    "1002079012",
    "1024723020",
    "1082442020",
    "1002005011",
    "1034190020",
    "1071057010",
    "1040251041",
    "1005895052",
    "1008406040",
    "1081759030",
    "1030502013",
    "1005355011",
    "1114587500",
    "1003402050",
    "1093943020",
    "1002431040",
    "1091184010",
    "1039520070",
    "1057850020",
    "6262",
    "1080481010",
    "1005454014",
    "1000033040",
    "1003970040",
    "1032966041",
    "1042560010",
    "1006245063",
    "1089576117",
    "1030346010",
    "1059989010",
    "1115076010",
    "1096186051",
    "1032544040",
    "1024533040",
    "1075190011",
    "1004722040",
    "1005958014",
    "1088358010",
    "1061096020",
    "1069508010",
    "1053290030",
    "1073161020",
    "1006664041",
    "1000394070",
    "1081577510",
    "1018729011",
    "1005083060",
    "1000728041",
    "1103505020",
    "1006125101",
    "1068581040",
    "1085418040",
    "1000134031",
    "1079829011",
    "1002401010",
    "1004557012",
    "1087561030",
    "1083324020",
    "1003339013",
    "1021754040",
    "1039297060",
    "1080296501",
    "1057262010",
    "1067228010",
    "1040791041",
    "1050800040",
    "1104097050",
    "1069068510",
    "1004990030",
    "1005110062",
    "1084616040",
    "1035076020",
    "1101410030",
    "1042974041",
    "1008406020",
    "1001534014",
    "1039297025",
    "1006521011",
    "1099917010",
    "1068469020",
    "1038944040",
    "1051067010",
    "1050178020",
    "1077530060",
    "1003978060",
    "1003402054",
    "1003492050",
    "1111672050",
    "1091771060",
    "1113098500",
    "1117337010",
    "1043803012",
    "1074457020",
    "1060734030",
    "1064276030",
    "1007306020",
    "1000468040",
    "1033301010",
    "1052397060",
    "1073564030",
    "1070197020",
    "1001779050",
    "1075667010",
    "1063597060",
    "1018863030",
    "1042046011",
    "1068554012",
    "1037363040",
    "1006953041",
    "1102870010",
    "1088253040",
    "1108932040",
    "1062182031",
    "1005750031",
    "1097564020",
    "1001918032",
    "1002373041",
    "1091381010",
    "1099996060",
    "1063602010",
    "1091788041",
    "1005315050",
    "1042081020",
    "1000406060",
    "1116938040",
    "1099092020",
    "1089299020",
    "1079655060",
    "1101719060",
    "1099174510",
    "1077238010",
    "1099863030",
    "1061267010",
    "1003131070",
    "1102241020",
    "1005057030",
    "1095523061",
    "1007425061",
    "1004855017",
    "1031972040",
    "1115880800",
    "1074402040",
    "1040253040",
    "1041015060",
    "1002574040",
    "1002806043",
    "1098973020",
    "1041074010",
    "1036444040",
    "1048666510",
    "1035683023",
    "1000459065",
    "1098631060",
    "1099288040",
    "1115076025",
    "1060733052",
    "1006246102",
    "1000601010",
    "1000725101",
    "1000028101",
    "1000143010",
    "1024738041",
    "1066993020",
    "1000044011",
    "1000198050",
    "1002800022",
    "1004897021",
    "1006303040",
    "1074395063",
    "1022582060",
    "1101110010",
    "1117066070",
    "1109895040",
    "1059986010",
    "1100942040",
    "1114171010",
    "1005130017",
    "1006665030",
    "1001475050",
    "1024532040",
    "1036310023",
    "1080997010",
    "1000028051",
    "1000113041",
    "1097787070",
    "1000821510",
    "1000209501",
    "1018477800",
    "1095836044",
    "1095836080",
    "1066748040",
    "1118918010"]

    for id in supplier_ids:
        rsp = proxy.get_suppliers({
            "supplierId": id,
            "isAdvertiser": "Y"
        })

        if not rsp["success"]:
            custom_logger.logger.error(f"Failed to get Supplier [{rsp}]")
            raise Exception(f"Failed to get Supplier [{id}]")

        if rsp["totalCount"] != 0:
            continue

        supplier_ids.append(id)

    custom_logger.logger.info(supplier_ids)

def main():


    run_ad_tests({
        'bu': 'WTCMY',
        'appid': 500,
        'sign_token': 'Gq5jNHINxQlKYkzlZ5FxEVwDNOKksXlQ',
        'client_id': 'mp-wtcmy-sit',
        'client_secret': 'W6wMtKuaa8SGeswOl8inOYfqGkKKdElC',
        'url':'https://sit.api.apac.aswatson.com'
    })

if __name__ == '__main__':
    main()